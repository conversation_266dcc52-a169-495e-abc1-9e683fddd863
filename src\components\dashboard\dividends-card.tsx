"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/bar-chart";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePortfolioDividends } from "@/hooks/use-portfolio-dividends";
import { SupportedCurrency } from "./currency-selector";
import { DividendTableRow } from "@/utils/db/dashboard-queries";
import { Loader2, Calendar } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";

interface DividendsCardProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

export function DividendsCard({
  selectedPortfolios,
  displayCurrency,
}: DividendsCardProps) {
  const {
    data: dividendsData,
    isLoading,
    error,
  } = usePortfolioDividends(selectedPortfolios, displayCurrency);

  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );
  const [viewMode, setViewMode] = useState<"yearly" | "monthly">("yearly");
  const [rangeMode, setRangeMode] = useState<"all" | "year">("all");

  if (isLoading) {
    return (
      <Card className="lg:col-span-2 h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Se încarcă datele...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="lg:col-span-2 h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <div className="text-lg font-medium mb-2">
              Eroare la încărcarea datelor
            </div>
            <div className="text-sm">
              {error instanceof Error ? error.message : "Eroare necunoscută"}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!dividendsData || dividendsData.yearlyData.length === 0) {
    return (
      <Card className="lg:col-span-2 h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <div className="text-lg font-medium mb-2">
              Nu există date de dividende
            </div>
            <div className="text-sm">
              Selectați un portofoliu cu tranzacții pentru a vedea dividendele
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare data for stacked bar chart (yearly)
  const chartData = dividendsData.yearlyData.map((yearData) => {
    const dataPoint: any = { year: yearData.year.toString() };
    yearData.assetBreakdown.forEach((asset) => {
      dataPoint[asset.ticker] = asset.amount;
    });
    return dataPoint;
  });

  // Get all unique tickers for chart categories (yearly)
  const allTickers = [
    ...new Set(
      dividendsData.yearlyData.flatMap((year) =>
        year.assetBreakdown.map((asset) => asset.ticker)
      )
    ),
  ];

  // Prepare monthly data for selected year
  const monthLabels = [
    "Ian",
    "Feb",
    "Mar",
    "Apr",
    "Mai",
    "Iun",
    "Iul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const monthlyMap = dividendsData.monthlyData?.[selectedYear] || {};
  const monthlyCategories = Array.from(
    new Set(
      Object.values(monthlyMap).flatMap((bucket) => Object.keys(bucket || {}))
    )
  );
  const chartDataMonthly = Array.from({ length: 12 }, (_, i) => {
    const monthIndex = i + 1;
    const entry: any = { month: monthLabels[i] };
    const bucket = (monthlyMap as any)[monthIndex] || {};
    Object.entries(bucket).forEach(([ticker, amount]) => {
      entry[ticker] = amount as number;
    });
    return entry;
  });
  const monthlyCatsForChart =
    monthlyCategories.length > 0 ? monthlyCategories : allTickers;
  const hasMonthlyData = chartDataMonthly.some((entry) =>
    monthlyCatsForChart.some((t) => Number(entry?.[t]) > 0)
  );

  // Use default diverse color palette for better visual contrast
  // This provides better differentiation between companies in stacked bars

  // Get table data for selected year
  const tableData = dividendsData.tableData[selectedYear] || [];

  return (
    <Card className="lg:col-span-2 h-[650px] flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle className="text-xl">Dividende</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto space-y-4">
        {/* Top Metrics Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="text-2xl font-bold text-portavio-blue">
              {formatCurrency(dividendsData.totalDividends, displayCurrency)}
            </div>
            <div className="text-sm text-muted-foreground">Totalul primit</div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-muted/50 rounded-lg p-4 cursor-help">
                  <div className="text-2xl font-bold text-portavio-blue">
                    {dividendsData.dividendYieldTTM > 0
                      ? formatPercentage(dividendsData.dividendYieldTTM)
                      : "N/A"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Dividend Yield (TTM)
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p>
                  Dividend Yield (TTM) reprezinta procentul dividendelor primite
                  în ultimele 12 luni, comparativ cu valoarea portofoliilor.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-muted/50 rounded-lg p-4 cursor-help">
                  <div className="text-2xl font-bold text-portavio-blue">
                    {dividendsData.yocTTM > 0
                      ? formatPercentage(dividendsData.yocTTM)
                      : "N/A"}
                  </div>
                  <div className="text-sm text-muted-foreground">YoC (TTM)</div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p>
                  Yield on Cost (YoC) arata procentul dividendelor primite în
                  ultimele 12 luni, comparativ cu costul inițial al
                  investițiilor.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-muted/50 rounded-lg p-4 cursor-help">
                  <div className="text-2xl font-bold text-portavio-blue">
                    {dividendsData.cagrPayouts > 0
                      ? formatPercentage(dividendsData.cagrPayouts)
                      : "N/A"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    CAGR (Payouts)
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p>
                  CAGR (Compound Annual Growth Rate) arata tendinta de creștere
                  a dividendelor pe an, bazându-se pe primul și ultimul an în
                  care ați primit dividende.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Bar Charts Section */}
        <div className="space-y-4">
          {/* Year selector buttons */}
          <div className="flex flex-wrap items-center gap-2">
            <Button
              variant="link"
              size="sm"
              className={`px-2 py-0 text-sm ${
                rangeMode === "all"
                  ? "text-primary font-semibold underline underline-offset-2"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              onClick={() => {
                setRangeMode("all");
                setViewMode("yearly");
                setSelectedYear(new Date().getFullYear());
              }}
            >
              Alltime
            </Button>
            {Array.from(
              { length: 6 },
              (_, i) => new Date().getFullYear() - i
            ).map((y) => (
              <Button
                key={y}
                variant="link"
                size="sm"
                className={`px-2 py-0 text-sm ${
                  rangeMode === "year" && selectedYear === y
                    ? "text-primary font-semibold underline underline-offset-2"
                    : "text-muted-foreground hover:text-foreground"
                }`}
                onClick={() => {
                  setRangeMode("year");
                  setSelectedYear(y);
                  setViewMode("monthly");
                }}
              >
                {y}
              </Button>
            ))}
          </div>

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              {viewMode === "yearly" ? (
                <span>
                  Dividende pe ani {rangeMode === "all" ? "(Alltime)" : ""}
                </span>
              ) : (
                <span>Dividende lunare pentru {selectedYear}</span>
              )}
            </div>
            {viewMode === "monthly" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setViewMode("yearly");
                  setRangeMode("all");
                  setSelectedYear(new Date().getFullYear());
                }}
              >
                Înapoi
              </Button>
            )}
          </div>

          {viewMode === "yearly" ? (
            chartData.length > 0 ? (
              <BarChart
                data={chartData}
                index="year"
                categories={allTickers}
                valueFormatter={(value) =>
                  formatCurrency(value, displayCurrency)
                }
                className="h-80"
                showLegend={true}
                showTooltip={true}
                type="stacked"
                xAxisSubLabelValue={(d) => {
                  const sum = allTickers.reduce(
                    (acc, t) => acc + (Number(d?.[t]) || 0),
                    0
                  );
                  return formatCurrency(sum, displayCurrency);
                }}
                onValueChange={(v) => {
                  if (!v) return;
                  const rawYear: any =
                    (v as any).year ?? (v as any).label ?? (v as any).index;
                  const yr =
                    typeof rawYear === "string"
                      ? parseInt(rawYear, 10)
                      : Number(rawYear);
                  if (!Number.isNaN(yr)) {
                    setSelectedYear(yr);
                    setViewMode("monthly");
                    setRangeMode("year");
                  }
                }}
              />
            ) : (
              <div className="h-80 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Nu există date de dividende
                  </div>
                  <div className="text-sm">
                    Pentru perioada selectată nu au fost găsite dividende
                  </div>
                </div>
              </div>
            )
          ) : hasMonthlyData ? (
            <BarChart
              data={chartDataMonthly}
              index="month"
              categories={monthlyCatsForChart}
              valueFormatter={(value) => formatCurrency(value, displayCurrency)}
              className="h-80"
              showLegend={true}
              showTooltip={true}
              type="stacked"
              xAxisSubLabelValue={(d) => {
                const sum = monthlyCatsForChart.reduce(
                  (acc, t) => acc + (Number(d?.[t]) || 0),
                  0
                );
                return formatCurrency(sum, displayCurrency);
              }}
            />
          ) : (
            <div className="h-80 flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-lg font-medium mb-2">
                  {`Nu există dividende pentru anul ${selectedYear}`}
                </div>
                <div className="text-sm">
                  Selectați un alt an din butoanele de mai sus pentru a vedea
                  dividendele
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Table Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              Detalii dividende
              <span className="text-sm text-muted-foreground">
                ({selectedYear})
              </span>
            </h3>
          </div>

          {tableData.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Nr. Plăți</TableHead>
                    <TableHead className="text-right">Valoare</TableHead>
                    <TableHead className="text-right">Yield</TableHead>
                    <TableHead className="text-right">YoY Growth</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((row: DividendTableRow) => (
                    <TableRow key={row.ticker}>
                      <TableCell>
                        <div className="text-left">
                          <div className="font-medium">{row.company}</div>
                          <div className="text-sm text-muted-foreground text-left">
                            {row.ticker}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-left">
                        <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                          {row.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {row.payments}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(row.payout, displayCurrency)}
                      </TableCell>
                      <TableCell className="text-right">
                        {row.yield > 0 ? formatPercentage(row.yield) : "N/A"}
                      </TableCell>
                      <TableCell className="text-right">
                        {row.yoyGrowth !== 0 ? (
                          <span
                            className={
                              row.yoyGrowth > 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {row.yoyGrowth > 0 ? "+" : ""}
                            {formatPercentage(Math.abs(row.yoyGrowth))}
                          </span>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Nu există dividende pentru {selectedYear}
              </div>
              <div className="text-sm">
                Selectați un an din butoanele de mai sus
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
